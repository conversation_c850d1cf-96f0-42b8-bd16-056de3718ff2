import { useEffect, useState } from 'react';
import { ChatMessage, Roles, DietPreferences, UserFeedback } from '@/constants/Types';
import { firestoreRepository, FirestoreCollections } from '@/repositories/firestoreRepository';
import { ResponseInputItem } from 'openai/resources/responses/responses';
import { LlmService } from '@/services/LlmService';
import { chatCompletionModel } from '@/constants/LlmConfigs';
import { useAuth } from '@/contexts/AuthContext';
import { dietPreferencesSchema } from '@/schemas/dietPreferences';
import { userFeedbackSchema } from '@/schemas/userFeedback';

const defaultInstruction = `You are a helpful cooking assistant. Answer questions, give meal ideas, nutrition tips, and help users to update their saved preferences.

IMPORTANT INSTRUCTIONS FOR SPECIAL CASES:

1. DIET PREFERENCE MODIFICATIONS:
When a user wants to modify their dietary preferences (allergies, diet type, cooking time, experience level, calorie goals, or dietary goals), you should:
- Extract the specific changes they want to make
- Respond with a JSON object wrapped in [SAVE_DIET_PREFERENCES] tags containing the modifications
- Format: [SAVE_DIET_PREFERENCES]{"modifications": "clear description of changes"}[/SAVE_DIET_PREFERENCES]
- Then provide a helpful response acknowledging the changes

2. USER FEEDBACK DETECTION:
If a user is providing feedback about the app, its features, user experience, recipe quality, bugs, or suggestions, you should:
- Extract the feedback and categorize it appropriately
- Respond with a JSON object wrapped in [SAVE_FEEDBACK] tags
- Format: [SAVE_FEEDBACK]{"feedback": "user's feedback", "category": "general|feature_request|bug_report|user_experience|recipe_quality|other"}[/SAVE_FEEDBACK]
- Then thank them for their feedback

Examples of feedback indicators:
- "The app is great but..."
- "I wish the app could..."
- "There's a bug when..."
- "The recipes are too complicated"
- "I love how the app..."
- "It would be better if..."

Keep your responses helpful, friendly, and focused on cooking, recipes, and nutrition. Always prioritize the user's cooking and dietary needs.`;

const defaultIntro: ChatMessage = {
  role: Roles.developer,
  content: `Would you like to update any dietary preference? I can also chat about meal ideas and nutrition tips. Here are your current preference settings:`,
};

export function useChatExperience() {
  const { user } = useAuth();
  const [messageInput, setMessageInput] = useState('');
  const [conversation, setConversation] = useState<ChatMessage[]>([defaultIntro]);
  const [context, setContext] = useState<ResponseInputItem[]>([]);
  const [isTyping, setIsTyping] = useState(true);

  // Fetch diet preferences when user is available
  useEffect(() => {
    if (user) {
      fetchAndShowDietPreferences(user.uid);
    }
  }, [user]);

  useEffect(() => {
    if (conversation.length) {
      storeConversation(conversation);
    }
  }, [conversation]);

  const fetchAndShowDietPreferences = async (userId: string) => {
    try {
      const doc = await firestoreRepository.getDocument(FirestoreCollections.DIET_PREFERENCES, userId);
      if (doc) {
        const preferences = doc as DietPreferences;
        const dietMessage = {
          role: Roles.developer,
          content: `User's dietary preferences are: ${JSON.stringify(preferences)}.`,
        };

        const summaryResponse = await LlmService.callLlmApi(
          chatCompletionModel,
          "Respond ONLY with a bullet points summary of the user's dietary preferences.",
          [dietMessage]
        );

        const outputText = LlmService.extractOutputText(summaryResponse);
        setIsTyping(false);
        setConversation((prev) => [...prev, { role: Roles.assistant, content: outputText }]);
      }
    } catch (error) {
      console.error('Error fetching diet preferences:', error);
    }
  };

  const storeConversation = async (conv: ChatMessage[]) => {
    if (!user?.uid) return;
    await firestoreRepository.addOrReplaceDocument(FirestoreCollections.CONVERSATIONS, user.uid, {
      conversation: conv,
    });
  };

  const handleSend = async () => {
    if (!messageInput.trim()) return;

    setTimeout(() => setIsTyping(true), 450);

    const userMessage = { role: Roles.user, content: messageInput };
    setConversation((prev) => [...prev, userMessage]);
    const input = messageInput;
    setMessageInput('');

    const apiInput: ResponseInputItem = { role: Roles.user, content: input };

    let outputText;
    try {
      const response = await LlmService.callLlmApi(chatCompletionModel, defaultInstruction, [...context, apiInput]);
      outputText = LlmService.extractOutputText(response);
    } catch (err) {
      console.error('LLM API error:', err);
      setIsTyping(false);
      return;
    }

    // Check for diet preference modifications
    if (outputText.includes('[SAVE_DIET_PREFERENCES]')) {
      await handleDietPreferenceModification(outputText, input);
      outputText = outputText.replace(/\[SAVE_DIET_PREFERENCES\].*?\[\/SAVE_DIET_PREFERENCES\]/s, '').trim();
      if (!outputText) {
        outputText = "I've updated your dietary preferences with the changes you mentioned.";
      }
    }

    // Check for user feedback
    if (outputText.includes('[SAVE_FEEDBACK]')) {
      await handleUserFeedback(outputText, input);
      outputText = outputText.replace(/\[SAVE_FEEDBACK\].*?\[\/SAVE_FEEDBACK\]/s, '').trim();
      if (!outputText) {
        outputText = "Thank you for your feedback! I've recorded your comments to help improve the app.";
      }
    }

    const assistantMessage = { role: Roles.assistant, content: outputText };
    setContext((prev) => [...prev, apiInput, assistantMessage]);
    setConversation((prev) => [...prev, assistantMessage]);
    setIsTyping(false);
  };

  const handleDietPreferenceModification = async (outputText: string, userInput: string) => {
    if (!user?.uid) return;

    try {
      // Get current preferences first
      const currentPrefs = await firestoreRepository.getDocument(FirestoreCollections.DIET_PREFERENCES, user.uid);
      if (!currentPrefs) return;

      // Extract the JSON from the output text
      const jsonMatch = outputText.match(/\[SAVE_DIET_PREFERENCES\](.*?)\[\/SAVE_DIET_PREFERENCES\]/s);
      if (!jsonMatch) {
        // Fallback: use LLM to extract and map modifications from user input
        const contextMessage = {
          role: Roles.developer,
          content: `Current user preferences: ${JSON.stringify(currentPrefs)}. User wants to modify: "${userInput}". Update the preferences object with the requested changes. If any changes cannot be mapped to the existing fields (allergies, diet, timeToCook, experience, calories, goals), put them in the notes field.`,
        };

        const response = await LlmService.callLlmApi(
          chatCompletionModel,
          "Update the user's dietary preferences based on their request. Respond with the complete updated preferences object.",
          [contextMessage],
          0.1,
          dietPreferencesSchema,
          'updated_preferences'
        );

        const extractedText = LlmService.extractOutputText(response);
        const updatedPrefs: DietPreferences = JSON.parse(extractedText);

        await firestoreRepository.addOrReplaceDocument(FirestoreCollections.DIET_PREFERENCES, user.uid, updatedPrefs);
      } else {
        // Parse the JSON from the LLM response
        const updatedPrefs: DietPreferences = JSON.parse(jsonMatch[1].trim());

        await firestoreRepository.addOrReplaceDocument(FirestoreCollections.DIET_PREFERENCES, user.uid, updatedPrefs);
      }
    } catch (error) {
      console.error('Error handling diet preference modification:', error);
    }
  };

  const handleUserFeedback = async (outputText: string, userInput: string) => {
    if (!user?.uid) return;

    try {
      // Extract the JSON from the output text
      const jsonMatch = outputText.match(/\[SAVE_FEEDBACK\](.*?)\[\/SAVE_FEEDBACK\]/s);
      if (!jsonMatch) {
        // Fallback: use LLM to extract feedback from user input
        const response = await LlmService.callLlmApi(
          chatCompletionModel,
          'Extract the user feedback and categorize it. Respond with a JSON object.',
          [{ role: Roles.user, content: userInput }],
          0.1,
          userFeedbackSchema,
          'user_feedback'
        );

        const extractedText = LlmService.extractOutputText(response);
        const feedback: UserFeedback = JSON.parse(extractedText);

        // Save feedback to Firestore
        const feedbackWithTimestamp = {
          ...feedback,
          timestamp: new Date(),
        };
        await firestoreRepository.addOrReplaceDocument(
          FirestoreCollections.USER_FEEDBACKS,
          user.uid,
          feedbackWithTimestamp
        );
      } else {
        // Parse the JSON from the LLM response
        const feedback: UserFeedback = JSON.parse(jsonMatch[1].trim());

        // Save feedback to Firestore
        const feedbackWithTimestamp = {
          ...feedback,
          timestamp: new Date(),
        };
        await firestoreRepository.addOrReplaceDocument(
          FirestoreCollections.USER_FEEDBACKS,
          user.uid,
          feedbackWithTimestamp
        );
      }
    } catch (error) {
      console.error('Error handling user feedback:', error);
    }
  };

  return {
    user,
    messageInput,
    setMessageInput,
    conversation,
    isTyping,
    handleSend,
  };
}
